"""PDK package for GDS cursor."""

import gdsfactory as gf
import inspect
from pathlib import Path
import importlib

from gdsfactory.typings import Layer, LayerSpec, ComponentSpec, CrossSectionSpec
from gdsfactory.component import Component
from gdsfactory.technology import LayerViews, LayerView

# 从相应模块导入定义
from pdk.layers import LAYER
from pdk import cross_sections

# -----------------
# 组件发现函数
# -----------------
def get_component_factories():
    """
    手动导入并注册所有组件。这是最可靠的方法。
    """
    # 导入所有组件模块
    from components.waveguide_with_electrodes import waveguide_tip_with_ppln_electrodes
    from components.alignment_marks import alignment_cross
    from components.text_labels import simple_text_label
    from components.dicing_marks import dicing_rectangle_mark, chip_dicing_grid
    from components.electrode import ppln_electrode
    from components.waveguide_tip import bend_s_offset, waveguide_with_tips
    from components.waveguide_zcut_electrode import periodic_electrode, waveguide_with_electrode
    
    # 尝试导入ring_with_electrode模块的组件（可能有问题，用try-except包装）
    ring_components = {}
    try:
        from components.ring_with_electrode import (
            create_ring_electrode_with_teeth,
            thermal_heater_ring, 
            ring_with_electrode,
            ring_with_electrode_with_heater
        )
        ring_components = {
            "create_ring_electrode_with_teeth": create_ring_electrode_with_teeth,
            "thermal_heater_ring": thermal_heater_ring,
            "ring_with_electrode": ring_with_electrode,
            "ring_with_electrode_with_heater": ring_with_electrode_with_heater,
        }
        print("✅ 成功导入ring_with_electrode模块组件")
    except Exception as e:
        print(f"⚠️  ring_with_electrode模块导入失败: {e}")
        print("    将跳过该模块的组件注册")

    # 手动构建组件字典
    component_factories = {
        # 基础组件
        "waveguide_tip_with_ppln_electrodes": waveguide_tip_with_ppln_electrodes,
        "alignment_cross": alignment_cross,
        "simple_text_label": simple_text_label,
        "dicing_rectangle_mark": dicing_rectangle_mark,
        "chip_dicing_grid": chip_dicing_grid,
        
        # 电极组件
        "ppln_electrode": ppln_electrode,
        
        # 波导组件
        "bend_s_offset": bend_s_offset,
        "waveguide_with_tips": waveguide_with_tips,
        "periodic_electrode": periodic_electrode,
        "waveguide_with_electrode": waveguide_with_electrode,
    }
    
    # 添加环形器件组件（如果导入成功）
    component_factories.update(ring_components)
    
    print(f"✅ 总共注册了 {len(component_factories)} 个组件:")
    for name in sorted(component_factories.keys()):
        print(f"    - {name}")
    
    return component_factories

# --- 创建一个干净的 LayerViews ---
# 这可以防止gdsfactory加载默认的、包含很多层的视图
layer_views = LayerViews()

# 定义层的颜色和样式
layer_colors = {
    'SUBSTRATE': '#c0c0c0',
    'WG': '#4040ff', 
    'METAL': '#ffd400',
    'HEATER': '#ff8080',
    'DICING': '#80c080',
    'TEXT': '#804080',
    'MARK': '#c0c040'
}

# 只添加我们自己定义的层到视图中
defined_layers = []

# 直接使用我们知道的层定义
layer_definitions = {
    'SUBSTRATE': (0, 0),
    'WG': (1, 0),
    'METAL': (41, 0),
    'HEATER': (49, 0),
    'DICING': (5, 0),
    'TEXT': (66, 0),
    'MARK': (101, 0)
}

for layer_name, layer_value in layer_definitions.items():
    color = layer_colors.get(layer_name, '#808080')  # 默认灰色
    layer_views.layer_views[layer_name] = LayerView(
        layer=layer_value,
        color=color
    )
    defined_layers.append(layer_name)

print(f"Defined layers found: {defined_layers}")

# 获取默认PDK的配置作为基础
default_pdk = gf.get_active_pdk()
print(f"继承默认PDK '{default_pdk.name}' 的配置...")

# 获取默认PDK的其他配置（排除我们要自定义的部分）
default_config = {k: v for k, v in default_pdk.__dict__.items() 
                  if k not in ['name', 'layers', 'layer_views', 'cross_sections', 'cells'] 
                  and not k.startswith('_')}

# 确保bend_points_distance是数值
if 'bend_points_distance' in default_config:
    if isinstance(default_config['bend_points_distance'], str):
        try:
            default_config['bend_points_distance'] = float(default_config['bend_points_distance'])
        except:
            default_config['bend_points_distance'] = 0.02  # 默认值
else:
    default_config['bend_points_distance'] = 0.02  # 确保存在

print(f"继承的默认配置: {list(default_config.keys())}")
print(f"bend_points_distance: {default_config['bend_points_distance']} (type: {type(default_config['bend_points_distance'])})")

# 创建自定义PDK，继承默认PDK的所有配置
PDK = gf.Pdk(
    name="MyPDK",
    # 使用我们自定义的配置
    layers=LAYER,
    layer_views=layer_views,
    
    # 合并cross_sections（默认的 + 自定义的）
    cross_sections={
        **getattr(default_pdk, 'cross_sections', {}),
        **{
            name: func
            for name, func in inspect.getmembers(cross_sections, inspect.isfunction)
            if inspect.signature(func).return_annotation == gf.CrossSection
        }
    },
    
    # 合并cells（默认的 + 自定义的）
    cells={
        **getattr(default_pdk, 'cells', {}),
        **get_component_factories(),
    },
    
    # 继承默认PDK的其他所有配置
    **default_config
)

# 确保bend_points_distance是正确的数值类型
PDK.bend_points_distance = 0.02

PDK.activate()

# 简化版的层检查
print(f"PDK activated with {len(defined_layers)} custom layers: {defined_layers}")

# 导出干净的层对象供全局使用
LAYERS = PDK.layers

__all__ = [
    "PDK",
    "LAYERS",
    "Layer",
    "LayerSpec",
    "Component",
    "ComponentSpec",
    "CrossSectionSpec",
]
