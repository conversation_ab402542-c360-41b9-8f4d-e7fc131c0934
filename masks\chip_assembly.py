"""简化的芯片组装脚本 - 专注于核心功能"""

import gdsfactory as gf
import yaml
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# 导入PDK
from pdk import PDK, LAYERS
from components.alignment_marks import add_alignment_marks
from components.dicing_marks import chip_dicing_grid


def load_config(config_path=None, force_matrix=False):
    """加载配置文件 - 简化版本"""
    if config_path is None:
        config_path = Path(__file__).parent / "config.yml"
    else:
        # 如果config_path是相对路径，基于脚本所在目录解析
        config_path = Path(config_path)
        if not config_path.is_absolute():
            config_path = Path(__file__).parent / config_path
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        # 检测多PCells格式 (config_mixed.yml)
        if ('pcells' in config_data and 'layout' in config_data and 
            'columns' in config_data.get('layout', {})):
            print("🐱 使用多PCells配置格式...")
            # 导入解析器
            from mixed_config_parser import parse_mixed_config
            
            result = parse_mixed_config(config_data)
            if result:
                print(f"✅ 解析成功! 生成了 {len(result.get('smallparts', {}))} 个smallparts")
                return result
        
        # 如果用户明确要求使用矩阵格式，或者检测到是矩阵格式
        if force_matrix or ('pcell' in config_data and 'x_offsets' in config_data.get('layout', {})):
            print("🐱 使用矩阵配置格式...")
            # 导入解析器（现在在同一个文件夹中）
            from simple_matrix_parser import parse_matrix_config
            
            result = parse_matrix_config(config_path)
            if result:
                print(f"✅ 解析成功! 生成了 {len(result.get('smallparts', {}))} 个smallparts")
                return result
        
        # 默认使用传统格式
        print("📋 使用传统配置格式...")
        return config_data
                
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return None


def create_component_grid(smallpart_config, pcell_name):
    """创建组件网格，支持参数扫描"""
    params = smallpart_config['parameters']
    
    # 分离扫描参数和固定参数
    sweep_params = {k: v for k, v in params.items() if isinstance(v, list)}
    fixed_params = {k: v for k, v in params.items() if not isinstance(v, list)}
    
    components = []
    
    if sweep_params:
        # 参数扫描：zip组合
        param_combinations = list(zip(*sweep_params.values()))
        for combo in param_combinations:
            current_params = fixed_params.copy()
            current_params.update(dict(zip(sweep_params.keys(), combo)))
            components.append(gf.get_component(pcell_name, **current_params))
    else:
        # 无扫描：创建count个相同组件
        count = smallpart_config['count']
        for _ in range(count):
            components.append(gf.get_component(pcell_name, **params))
    
    return components


def create_smallpart_with_label(smallpart_name, smallpart_config, layout_config, row, col, config):
    """创建带标签的smallpart"""
    pcell_name = smallpart_config['pcell_name']
    components = create_component_grid(smallpart_config, pcell_name)
    components.reverse() # 反向排列
    
    # 创建网格 - 简单垂直排列
    spacing = tuple(layout_config['grid']['spacing'])
    grid = gf.grid(components, spacing=spacing, shape=(len(components), 1))
    
    # 创建标签 (A1, B2格式)
    col_letter = chr(ord('A') + col)
    row_number = row + 1
    label_text = f"{col_letter}{row_number}"
    
    text_layer = gf.get_active_pdk().get_layer("TEXT")
    label = gf.components.text(text=label_text, size=100, layer=text_layer)
    
    # 组合网格和标签
    combined = gf.Component(f"labeled_{smallpart_name}")
    grid_ref = combined.add_ref(grid)
    label_ref = combined.add_ref(label)
    label_ref.dmovey(grid_ref.ymax + layout_config.get('label_offset', 30))
    # 添加切割标记
    if smallpart_config.get('add_dicing_marks', False):
        add_dicing_marks(combined, smallpart_config, config)
    return combined


def add_dicing_marks(combined, smallpart_config, config):
    """添加切割标记"""
    dicing_config = config.get('smallpart_dicing', {})
    if not dicing_config.get('enabled', True):
        return
    
    # 获取参数
    layer = gf.get_active_pdk().get_layer(dicing_config.get('layer', 'DICING'))
    size = dicing_config.get('size', [50, 20])
    offset_x = smallpart_config.get('dicing_offset_x', 0.0)
    offset_y = smallpart_config.get('dicing_offset_y', 0.0)
    
    # 创建标记并放置在四角
    mark = gf.components.rectangle(size=size, layer=layer, centered=True)
    positions = [
        (combined.xmin - offset_x, combined.ymax + offset_y),
        (combined.xmax + offset_x, combined.ymax + offset_y),
        (combined.xmin - offset_x, combined.ymin - offset_y),
        (combined.xmax + offset_x, combined.ymin - offset_y),
    ]
    
    for pos in positions:
        ref = combined.add_ref(mark)
        ref.dcenter = pos


def add_chip_dicing_marks(chip, main_grid, grid_shape, chip_dicing_config):
    """添加芯片级切割标记 - 适配gdsfactory 9.9.4"""
    if not chip_dicing_config.get('enabled', False):
        return
    
    # 获取配置参数
    layer = gf.get_active_pdk().get_layer(chip_dicing_config.get('layer', 'DICING'))
    mark_width = chip_dicing_config.get('mark_width', 20.0)
    mark_height = chip_dicing_config.get('mark_height', 50.0)
    boundary_xoffset = chip_dicing_config.get('boundary_xoffset', 200.0)
    boundary_yoffset = chip_dicing_config.get('boundary_yoffset', 200.0)
    
    # 使用新的kfactory API获取chip中的引用
    grid_ref = None
    try:
        # 遍历chip中的所有实例
        for inst in chip.insts:
            if hasattr(inst, 'cell') and inst.cell == main_grid:
                grid_ref = inst
                break
    except Exception as e:
        print(f"警告：无法访问chip.insts: {e}")
    
    if grid_ref is None:
        print("错误：无法找到main_grid在chip中的引用")
        return
    
    # 获取grid_ref的变换信息并处理单位转换
    grid_trans = grid_ref.cplx_trans
    grid_x_raw, grid_y_raw = grid_trans.disp.x, grid_trans.disp.y
    unit_factor_for_position = 1000.0 if abs(grid_x_raw) > 1000000 else 1.0
    grid_x = grid_x_raw / unit_factor_for_position
    grid_y = grid_y_raw / unit_factor_for_position
    
    # 收集smallpart的全局物理坐标
    smallpart_positions = []
    
    # 获取main_grid中的所有实例
    try:
        main_grid_insts = list(main_grid.insts)
        if not main_grid_insts:
            print("警告：main_grid中没有实例")
            return
            
        # 计算smallpart的全局坐标
        for i, inst in enumerate(main_grid_insts):
            try:
                # 使用实例的坐标属性转换为全局坐标
                global_xmin = grid_x + inst.xmin
                global_xmax = grid_x + inst.xmax  
                global_ymin = grid_y + inst.ymin
                global_ymax = grid_y + inst.ymax
                
                smallpart_positions.append((global_xmin, global_xmax, global_ymin, global_ymax))
                      
            except Exception as e:
                print(f"警告：处理实例 {i} 时出错: {e}")
                continue
                
    except Exception as e:
        print(f"错误：无法获取main_grid的实例列表: {e}")
        return
    
    if not smallpart_positions:
        print("错误：没有有效的smallpart位置信息")
        return
    
    # 创建芯片级切割网格
    try:
        dicing_grid = chip_dicing_grid(
            smallpart_positions=smallpart_positions,
            grid_shape=grid_shape,
            mark_width=mark_width,
            mark_height=mark_height,
            boundary_xoffset=boundary_xoffset,
            boundary_yoffset=boundary_yoffset,
            layer=layer
        )
        chip.add_ref(dicing_grid)
        print("✅ 芯片级切割标记添加成功")
        
    except Exception as e:
        print(f"错误：创建芯片级切割网格失败: {e}")


def create_chip_layout(config):
    """创建完整的芯片布局 - 使用自定义布局算法"""
    # 芯片设置
    chip_settings = config['chip_settings']
    chip_width, chip_height = chip_settings['size']
    chip_name = chip_settings['name']
    spacing = tuple(chip_settings['spacing'])
    
    # 创建芯片基底
    chip = gf.Component(chip_name)
    
    # 检查并修正单位转换问题 (gdsfactory 9.9.4可能有单位问题)
    test_rect = gf.components.rectangle(size=(1000.0, 1000.0), layer=gf.get_active_pdk().get_layer("SUBSTRATE"))
    unit_factor = 1000.0 if abs(test_rect.xmax - test_rect.xmin) < 2 else 1.0
    
    substrate = gf.components.rectangle(
        size=(chip_width / unit_factor, chip_height / unit_factor),
        layer=gf.get_active_pdk().get_layer("SUBSTRATE"),
        centered=True
    )
    chip.add_ref(substrate)
    
    # 收集并创建所有smallparts
    smallparts_layout = config['smallparts_layout']
    smallparts_config = config['smallparts']
    
    # 计算网格形状
    max_row = max(layout['row'] for layout in smallparts_layout.values())
    max_col = max(layout['col'] for layout in smallparts_layout.values())
    grid_shape = (max_row + 1, max_col + 1)
    
    # 创建smallpart组件矩阵
    smallpart_matrix = {}
    for name, layout_config in smallparts_layout.items():
        if name in smallparts_config:
            row, col = layout_config['row'], layout_config['col']
            smallpart = create_smallpart_with_label(
                name, smallparts_config[name], layout_config, row, col, config
            )
            smallpart_matrix[(row, col)] = smallpart
    
    # 使用自定义布局算法进行精确排列
    main_grid = gf.Component("main_grid")
    
    # 计算每行的位置和高度
    row_positions = []
    current_y = 0
    
    for row in range(grid_shape[0]):
        # 计算这一行的最大高度
        row_height = 0
        for col in range(grid_shape[1]):
            if (row, col) in smallpart_matrix:
                comp = smallpart_matrix[(row, col)]
                row_height = max(row_height, comp.ysize)
        
        # 记录行的顶部位置和高度
        row_positions.append((current_y, row_height))
        
        # 移动到下一行：当前行底部 - 间距
        current_y -= (row_height + spacing[1])
    
    # 放置所有smallpart组件
    smallpart_refs = []  # 存储所有smallpart引用，用于后续偏移调整
    
    for row in range(grid_shape[0]):
        current_x = 0
        row_top_y, row_height = row_positions[row]
        
        # 放置这一行的组件
        for col in range(grid_shape[1]):
            if (row, col) in smallpart_matrix:
                comp = smallpart_matrix[(row, col)]
                ref = main_grid.add_ref(comp)
                
                # X方向：紧挨着放置
                ref.xmin = current_x
                current_x = ref.xmax
                
                # Y方向：顶部对齐到行顶部
                ref.ymax = row_top_y
                
                # 存储引用和位置信息，用于偏移调整
                smallpart_refs.append((ref, row, col))
    
    # 将main_grid居中放置到芯片中心
    grid_ref = chip.add_ref(main_grid)
    main_grid_center_x = (main_grid.xmin + main_grid.xmax) / 2
    main_grid_center_y = (main_grid.ymin + main_grid.ymax) / 2
    grid_ref.dmove([-main_grid_center_x, -main_grid_center_y])
    
    # 应用单个smallpart的偏移
    for ref, row, col in smallpart_refs:
        # 查找对应的布局配置
        for name, layout_config in smallparts_layout.items():
            if layout_config['row'] == row and layout_config['col'] == col:
                x_offset = layout_config.get('x_offset', 0.0)
                y_offset = layout_config.get('y_offset', 0.0)
                
                if x_offset != 0.0 or y_offset != 0.0:
                    ref.dmove([x_offset, y_offset])
                break
    
    # 添加对准标记
    alignment_config = config.get('alignment_marks', {})
    if alignment_config.get('enabled', True):
        layer = gf.get_active_pdk().get_layer(alignment_config.get('layer', 'MARK'))
        add_alignment_marks(
            chip=chip, chip_width=chip_width, chip_height=chip_height, layer=layer,
            **{k: v for k, v in alignment_config.items() if k not in ['enabled', 'layer']}
        )
    
    # 添加芯片级切割标记 (新增功能)
    chip_dicing_config = config.get('chip_dicing', {})
    if chip_dicing_config.get('enabled', False):
        add_chip_dicing_marks(chip, main_grid, grid_shape, chip_dicing_config)
    
    return chip


def main():
    """主函数 - 小猫咪直接在这里设置配置"""
    
    # 确保我们的自定义PDK被激活
    print(f"🔧 当前活动PDK: {gf.get_active_pdk().name}")
    if gf.get_active_pdk().name != "MyPDK":
        print("⚠️  重新激活自定义PDK...")
        PDK.activate()
        print(f"✅ 已激活PDK: {gf.get_active_pdk().name}")
    
    # 验证bend_points_distance的状态
    pdk = gf.get_active_pdk()
    print(f"📏 bend_points_distance: {pdk.bend_points_distance} (类型: {type(pdk.bend_points_distance)})")
    
    # 🐱 小猫咪在这里选择配置文件和输出设置
    # ===================================================
    
    # 配置文件选择（选一个取消注释）
    #config_file = "config_simple.yml"         # 矩阵格式配置
    #config_file = "config.yml"                 # 传统格式配置
    config_file = "config_mixed.yml"       # 混合pcell配置
    
    # 输出文件设置（自动根据配置文件名生成，基于脚本位置）
    config_name = Path(config_file).stem  # 获取文件名（不含扩展名）
    script_dir = Path(__file__).parent  # 脚本所在目录（masks文件夹）
    gds_dir = script_dir.parent / "gds"  # gds文件夹
    output_file = gds_dir / f"{config_name}.gds"
    
    # 强制使用矩阵解析（通常不需要改）
    force_matrix = False
    
    # ===================================================
    
    print(f"🐱 小猫咪选择的配置: {config_file}")
    print(f"📁 输出文件: {output_file}")
    
    # 加载配置
    config = load_config(config_file, force_matrix=force_matrix)
    
    if not config:
        print("❌ 配置加载失败!")
        return
    
    # 创建芯片布局
    print("🔧 正在生成芯片布局...")
    chip = create_chip_layout(config)
    
    # 保存GDS文件
    chip.write_gds(output_file, with_metadata=False)
    print(f"✅ GDS文件已保存: {output_file}")
    
    # 自动显示
    print("👀 正在打开查看器...")
    chip.show()


@gf.cell  
def chip_assembly(config_path=None):
    """GDSFactory标准cell接口"""
    config = load_config(config_path)
    return create_chip_layout(config)



if __name__ == "__main__":
    # 确保使用正确的Python版本
    import sys    
    main()
